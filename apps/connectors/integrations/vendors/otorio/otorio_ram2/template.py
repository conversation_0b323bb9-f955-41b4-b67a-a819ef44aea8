from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class OtorioRam2Template(Template):
    id = "otorio_ram2"
    name = "OTORIO RAM2"
    category = Template.Category.OT_SECURITY
    versions = {}
    vendor = Vendors.OTORIO

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

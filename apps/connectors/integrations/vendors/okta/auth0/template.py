from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class Auth0Template(Template):
    id = "auth0"
    name = "Auth0"
    category = Template.Category.IDENTITY_SECURITY
    versions = {}
    vendor = Vendors.OKTA

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

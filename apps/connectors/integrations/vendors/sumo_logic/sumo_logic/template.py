from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class SumoLogicTemplate(Template):
    id = "sumo_logic"
    name = "Sumo Logic"
    category = Template.Category.SIEM
    versions = {}
    vendor = Vendors.SUMO_LOGIC

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

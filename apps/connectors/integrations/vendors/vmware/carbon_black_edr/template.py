from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class CarbonBlackEdrTemplate(Template):
    id = "carbon_black_edr"
    name = "VMware Carbon Black EDR"
    category = Template.Category.ENDPOINT_SECURITY
    versions = {}
    vendor = Vendors.VMWARE

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

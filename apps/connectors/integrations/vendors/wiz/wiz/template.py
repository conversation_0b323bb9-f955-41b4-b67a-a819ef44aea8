from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class WizTemplate(Template):
    id = "wiz"
    name = "Wiz"
    category = Template.Category.CLOUD_SECURITY
    versions = {}
    vendor = Vendors.WIZ

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

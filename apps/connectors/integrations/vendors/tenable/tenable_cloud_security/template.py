from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class TenableCloudSecurityTemplate(Template):
    id = "tenable_cloud_security"
    name = "Tenable Cloud Security"
    category = Template.Category.CLOUD_SECURITY
    versions = {}
    vendor = Vendors.TENABLE

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

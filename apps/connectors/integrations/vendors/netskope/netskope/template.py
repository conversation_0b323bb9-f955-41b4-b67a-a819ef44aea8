from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class NetskopeTemplate(Template):
    id = "netskope"
    name = "Netskope"
    category = Template.Category.SAAS_SECURITY
    versions = {}
    vendor = Vendors.NETSKOPE

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors


class CiscoMerakiTemplate(Template):
    id = "cisco_meraki"
    name = "Cisco Meraki"
    category = Template.Category.NETWORK_SECURITY
    versions = {}
    vendor = Vendors.CISCO

    @property
    def is_alert_source(self) -> bool:
        # we categorize this as an alert source even though we
        # don't currently have an EVENT_SYNC action for it
        return True

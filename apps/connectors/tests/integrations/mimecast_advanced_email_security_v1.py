"""
Tests for Mimecast Advanced Email Security V1 integration.

This test module uses JSON data files stored in the data/mimecast directory
rather than hardcoding test data directly in the test file. This approach:

1. Centralizes test data in dedicated files
2. Makes the test code cleaner and more maintainable
3. Allows for easier updates to test data
4. Follows the pattern used in other integration tests

Data files are loaded via a cached function to improve performance.
"""

import json
from functools import cache
from unittest import mock

import responses

import apps.connectors.integrations.vendors.mimecast.mimecast_advanced_email_security.v1.actions.event_sync as event_sync
import apps.connectors.integrations.vendors.mimecast.mimecast_advanced_email_security.v1.api as mimecast_api
from apps.connectors.health_checks.components.component import (
    HealthCheckComponent,
    HealthCheckRequirement,
    ValidationStatus,
)
from apps.connectors.integrations import IntegrationActionType
from apps.connectors.integrations.actions.decode_url import DecodeUrlArgs
from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
)
from apps.connectors.integrations.schemas.identifiers import UrlIdentifier
from apps.connectors.integrations.schemas.ocsf import (
    ControlAction,
    Disposition,
    EmailActivityDirection,
    OSINTIndicatorType,
    UrlCategory,
)
from apps.connectors.integrations.vendors.mimecast.mimecast_advanced_email_security.v1.bookmarks import (
    MimecastAdvancedEmailSecurityEventSyncBookmarks,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


@cache
def load_data(filename):
    """Load test data from JSON files."""
    path = "apps/connectors/tests/integrations/data/mimecast"
    with open(f"{path}/{filename}.json", "r") as f:
        return json.load(f)


def mock_message_finder_response():
    return load_data("message_finder_response")


def mock_siem_logs_response():
    return load_data("siem_logs_response")


def mock_ttp_url_logs_response(paginate=False):
    if paginate:
        return load_data("ttp_url_logs_response_paginated")
    else:
        return load_data("ttp_url_logs_response")


def mock_ttp_attachment_logs_response():
    return load_data("ttp_attachment_logs_response")


def mock_ttp_impersonation_logs_response():
    return load_data("ttp_impersonation_logs_response")


def mock_decode_url_response():
    return load_data("decode_url_response")


def mock_auth_response():
    responses.add(
        responses.POST,
        "https://test_base_url.com/api/login",
        json={
            "data": [{"accessKey": "test_access_key", "secretKey": "test_secret_key"}]
        },
        status=200,
    )


def mock_responses():
    mock_auth_response()

    responses.add(
        responses.POST,
        "https://test_base_url.com/api/message-finder/search",
        json=mock_message_finder_response(),
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_base_url.com/api/audit/get-siem-logs",
        json=mock_siem_logs_response(),
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_base_url.com/api/ttp/url/get-logs",
        json=mock_ttp_url_logs_response(paginate=True),
        match=[
            responses.matchers.json_params_matcher(
                {
                    "meta": {"pagination": {"pageSize": 100}},
                    "data": [
                        {
                            "from": mock.ANY,
                            "to": mock.ANY,
                        }
                    ],
                }
            )
        ],
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_base_url.com/api/ttp/url/get-logs",
        json=mock_ttp_url_logs_response(),
        match=[
            responses.matchers.json_params_matcher(
                {
                    "meta": {"pagination": {"pageSize": 100, "pageToken": "12345"}},
                    "data": [
                        {
                            "from": mock.ANY,
                            "to": mock.ANY,
                        }
                    ],
                }
            )
        ],
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_base_url.com/api/ttp/url/get-logs",
        json=load_data("empty_response"),
        match=[
            responses.matchers.json_params_matcher(
                {
                    "meta": {"pagination": {"pageSize": 100, "pageToken": mock.ANY}},
                    "data": [
                        {
                            "from": mock.ANY,
                            "to": mock.ANY,
                        }
                    ],
                }
            )
        ],
        status=200,
    )

    responses.add(
        responses.POST,
        "https://test_base_url.com/api/ttp/attachment/get-logs",
        json=mock_ttp_attachment_logs_response(),
        match=[
            responses.matchers.json_params_matcher(
                {
                    "meta": {"pagination": {"pageSize": 100}},
                    "data": [
                        {
                            "from": mock.ANY,
                            "to": mock.ANY,
                        }
                    ],
                }
            )
        ],
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_base_url.com/api/ttp/impersonation/get-logs",
        json=mock_ttp_impersonation_logs_response(),
        match=[
            responses.matchers.json_params_matcher(
                {
                    "meta": {"pagination": {"pageSize": 100}},
                    "data": [
                        {
                            "from": mock.ANY,
                            "to": mock.ANY,
                        }
                    ],
                }
            )
        ],
        status=200,
    )

    responses.add(
        responses.POST,
        "https://test_base_url.com/api/ttp/attachment/get-logs",
        json=load_data("empty_response"),
        match=[
            responses.matchers.json_params_matcher(
                {
                    "meta": {"pagination": {"pageSize": 100, "pageToken": mock.ANY}},
                    "data": [
                        {
                            "from": mock.ANY,
                            "to": mock.ANY,
                        }
                    ],
                }
            )
        ],
        status=200,
    )
    responses.add(
        responses.POST,
        "https://test_base_url.com/api/ttp/impersonation/get-logs",
        json=load_data("empty_response"),
        match=[
            responses.matchers.json_params_matcher(
                {
                    "meta": {"pagination": {"pageSize": 100, "pageToken": mock.ANY}},
                    "data": [
                        {
                            "from": mock.ANY,
                            "to": mock.ANY,
                        }
                    ],
                }
            )
        ],
        status=200,
    )


class MimecastAdvancedEmailSecurityV1ApiTest(BaseTestCase):
    @responses.activate
    def test_login_and_hmac_headers(self):
        mock_responses()
        api = ConnectorFactory.get_api(technology_id="mimecast_advanced_email_security")

        headers = api.get_authorization_headers("/api/ttp/url/get-logs")
        assert "Authorization" in headers
        assert headers["x-mc-app-id"] == api.app_id

    @responses.activate
    def test_message_finder_search(self):
        mock_responses()
        api = ConnectorFactory.get_api(technology_id="mimecast_advanced_email_security")
        # Mock message finder response with realistic trackedEmails data

        result = api.message_finder_search(searchQuery="test")
        self.assertEqual(result, mock_message_finder_response())

    @responses.activate
    def test_get_siem_logs(self):
        mock_responses()
        api = ConnectorFactory.get_api(technology_id="mimecast_advanced_email_security")
        # Mock SIEM Logs API response with realistic data

        result = api.get_siem_logs()
        self.assertEqual(result, mock_siem_logs_response())

    @responses.activate
    def test_get_ttp_url_logs(self):
        mock_responses()
        api = ConnectorFactory.get_api(technology_id="mimecast_advanced_email_security")

        result = api.get_ttp_url_logs(
            from_date="2024-01-01T00:00:00Z", to_date="2024-12-31T23:59:59Z"
        )
        self.assertEqual(result, mock_ttp_url_logs_response(paginate=True))

    @responses.activate
    def test_get_ttp_attachment_logs(self):
        mock_responses()
        api = ConnectorFactory.get_api(technology_id="mimecast_advanced_email_security")
        # Mock TTP Attachment Protection Logs API response with realistic data
        result = api.get_ttp_attachment_logs(
            from_date="2024-01-01T00:00:00Z", to_date="2024-12-31T23:59:59Z"
        )
        self.assertEqual(result, mock_ttp_attachment_logs_response())

    @responses.activate
    def test_get_ttp_impersonation_logs(self):
        mock_responses()
        api = ConnectorFactory.get_api(technology_id="mimecast_advanced_email_security")
        # Mock TTP Impersonation Protect Logs API response with realistic data

        result = api.get_ttp_impersonation_logs(
            from_date="2024-01-01T00:00:00Z", to_date="2024-12-31T23:59:59Z"
        )
        self.assertEqual(result, mock_ttp_impersonation_logs_response())

    @responses.activate
    def test_decode_url(self):
        mock_auth_response()
        responses.add(
            responses.POST,
            "https://test_base_url.com/api/ttp/url/decode-url",
            json=mock_decode_url_response(),
            status=200,
        )

        api = ConnectorFactory.get_api(technology_id="mimecast_advanced_email_security")
        result = api.decode_url("https://protect-xx.mimecast.com/s/encoded-url")
        self.assertEqual(result, mock_decode_url_response())


def setup_event_sync_responses():
    mock_responses()


class MimecastAdvancedEmailSecurityV1IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        self.integration = ConnectorFactory.get_integration(
            technology_id="mimecast_advanced_email_security",
            version_id="v1",
        )

    def test_bookmarks(self):
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        schema = MimecastAdvancedEmailSecurityEventSyncBookmarks.model_json_schema()
        self.assertIn(IntegrationActionType.EVENT_SYNC.value, schema["properties"])

        schema = schema["properties"][IntegrationActionType.EVENT_SYNC.value]
        self.assertIn("time_range_start", schema["properties"])

    @responses.activate
    def test_event_sync(self):
        setup_event_sync_responses()
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)
        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )
        result = serialize(list(response))
        self.assertEqual(
            result,
            [
                {
                    "raw_event": {
                        "event": mock_ttp_url_logs_response()["data"][0]["clickLogs"][
                            0
                        ],
                        "log_type": "clickLogs",
                    },
                    "event_timestamp": "2024-06-09T14:23:45Z",
                    "ocsf": {
                        "action": "Denied",
                        "action_id": 2,
                        "activity_id": 0,
                        "activity_name": "Unknown",
                        "category_name": "Network Activity",
                        "category_uid": 4,
                        "class_name": "Network Activity",
                        "class_uid": 4001,
                        "disposition": "Dropped",
                        "disposition_id": 6,
                        "message": "malicious",
                        "metadata": {
                            "correlation_uid": "2374823746823746",
                            "event_code": "URL contains suspicious domain with "
                            "recent registration and mimics "
                            "legitimate invoice naming pattern",
                            "uid": "2374823746823746",
                            "profiles": [],
                            "version": "1.5.0-dev",
                        },
                        "osint": [
                            {"email": {"to": ["<EMAIL>"]}, "value": ""}
                        ],
                        "time": 1717943025000,
                        "time_dt": "2024-06-09T14:23:45Z",
                        "type_name": "Network Activity: Unknown",
                        "type_uid": 400100,
                        "url": {
                            "categories": ["Other"],
                            "category_ids": [99],
                            "hostname": "suspicious-site.com",
                            "netloc": "suspicious-site.com",
                            "path": "/invoice-2024.pdf",
                            "scheme": "https",
                            "url_string": "https://suspicious-site.com/invoice-2024.pdf",
                        },
                    },
                },
                {
                    "raw_event": {
                        "event": mock_ttp_url_logs_response()["data"][0]["clickLogs"][
                            0
                        ],
                        "log_type": "clickLogs",
                    },
                    "event_timestamp": "2024-06-09T14:23:45Z",
                    "ocsf": {
                        "action": "Denied",
                        "action_id": 2,
                        "activity_id": 0,
                        "activity_name": "Unknown",
                        "category_name": "Network Activity",
                        "category_uid": 4,
                        "class_name": "Network Activity",
                        "class_uid": 4001,
                        "disposition": "Dropped",
                        "disposition_id": 6,
                        "message": "malicious",
                        "metadata": {
                            "correlation_uid": "2374823746823746",
                            "event_code": "URL contains suspicious domain with "
                            "recent registration and mimics "
                            "legitimate invoice naming pattern",
                            "uid": "2374823746823746",
                            "profiles": [],
                            "version": "1.5.0-dev",
                        },
                        "osint": [
                            {"email": {"to": ["<EMAIL>"]}, "value": ""}
                        ],
                        "time": 1717943025000,
                        "time_dt": "2024-06-09T14:23:45Z",
                        "type_name": "Network Activity: Unknown",
                        "type_uid": 400100,
                        "url": {
                            "categories": ["Other"],
                            "category_ids": [99],
                            "hostname": "suspicious-site.com",
                            "netloc": "suspicious-site.com",
                            "path": "/invoice-2024.pdf",
                            "scheme": "https",
                            "url_string": "https://suspicious-site.com/invoice-2024.pdf",
                        },
                    },
                },
                {
                    "raw_event": {
                        "event": mock_ttp_attachment_logs_response()["data"][0][
                            "attachmentLogs"
                        ][0],
                        "log_type": "attachmentLogs",
                    },
                    "event_timestamp": "2025-06-04T12:34:56Z",
                    "ocsf": {
                        "action": "quarantined",
                        "action_id": 99,
                        "activity_id": 99,
                        "activity_name": "Other",
                        "category_name": "Network Activity",
                        "category_uid": 4,
                        "class_name": "Email Activity",
                        "class_uid": 4009,
                        "direction": "Inbound",
                        "direction_id": 1,
                        "disposition": "malicious",
                        "disposition_id": 99,
                        "email": {
                            "files": [
                                {
                                    "hashes": [
                                        {
                                            "algorithm": "SHA-256",
                                            "algorithm_id": 3,
                                            "value": "2fab740f143fc1aa4c1cd0146d334c5593b1428f6d062b2c406e5efe8abe95ca",
                                        }
                                    ],
                                    "mime_type": "pdf",
                                    "name": "invoice.pdf",
                                }
                            ],
                            "from_mailbox": "<EMAIL>",
                            "to": ["<EMAIL>"],
                        },
                        "message": "Detected by sandbox analysis.",
                        "metadata": {
                            "correlation_uid": "2374823746823746",
                            "event_code": None,
                            "profiles": [],
                            "uid": "2374823746823746",
                            "version": "1.5.0-dev",
                        },
                        "time": 1749040496000,
                        "time_dt": "2025-06-04T12:34:56Z",
                        "type_name": "Email Activity: Other",
                        "type_uid": 400999,
                    },
                },
                {
                    "raw_event": {
                        "event": mock_ttp_impersonation_logs_response()["data"][0][
                            "impersonationLogs"
                        ][0],
                        "log_type": "impersonationLogs",
                    },
                    "event_timestamp": "2024-06-09T16:45:23Z",
                    "ocsf": {
                        "action": "hold",
                        "action_id": 99,
                        "activity_id": 99,
                        "activity_name": "(99, 'Other')",
                        "category_name": "Network Activity",
                        "category_uid": 4,
                        "class_name": "Email Activity",
                        "class_uid": 4009,
                        "email": {
                            "from_mailbox": "<EMAIL>",
                            "message_uid": "<<EMAIL>>",
                            "subject": "Urgent: Wire Transfer Required Today",
                            "to": ["<EMAIL>"],
                            "x_originating_ip": ["**************"],
                        },
                        "message": "Executive_Impersonation_Policy",
                        "metadata": {
                            "correlation_uid": "eNqrVipOTS9OLsosKVayqlFKS8wpTuWqBQAcKAgl",
                            "event_code": None,
                            "profiles": [],
                            "uid": "eNqrVipOTS9OLsosKVayqlFKS8wpTuWqBQAcKAgl",
                            "version": "1.5.0-dev",
                        },
                        "osint": [
                            {
                                "type": "Domain",
                                "type_id": 2,
                                "uid": "similar_internal_domain",
                                "value": "companny.com",
                            }
                        ],
                        "policy": {"name": "Executive_Impersonation_Policy"},
                        "time": 1717951523000,
                        "time_dt": "2024-06-09T16:45:23Z",
                        "type_name": "Email Activity: (99, 'Other')",
                        "type_uid": 400999,
                    },
                },
            ],
        )

    @responses.activate
    def test_decode_url_action(self):
        mock_auth_response()
        responses.add(
            responses.POST,
            "https://test_base_url.com/api/ttp/url/decode-url",
            json=mock_decode_url_response(),
            status=200,
        )

        result = self.integration.invoke_action(
            IntegrationActionType.DECODE_URL,
            action_args=DecodeUrlArgs(
                url=UrlIdentifier(value="https://protect-xx.mimecast.com/s/encoded-url")
            ),
        )

        self.assertIsNotNone(result)
        self.assertEqual(result.result.url_string, "https://example.com/original-url")


def setup_basic_responses(fail=False):
    mock_auth_response()
    if fail:
        responses.add(
            responses.POST,
            "https://test_base_url.com/api/ttp/url/get-logs",
            json={},
            status=400,
        )
    else:
        responses.add(
            responses.POST,
            "https://test_base_url.com/api/ttp/url/get-logs",
            json=mock_ttp_url_logs_response(),
            status=200,
        )


class MimecastAdvancedEmailSecurityV1HealthCheckComponentsTest(
    BaseTestCase, HealthCheckComponentTestMixin
):
    def setUp(self) -> None:
        self._patch_encryption()

        self.connector = ConnectorFactory(
            technology_id="mimecast_advanced_email_security",
            enabled_actions=["event_sync"],
        )

        self.integration = self.connector.get_integration(decrypt_config=False)

    @responses.activate
    def test_connection(self):
        setup_basic_responses()
        components = HealthCheckComponent.get_components(connector=self.connector)

        permissions_checks_expected = [
            HealthCheckRequirement(
                name="Read logs",
                description="Read logs from Mimecast Advanced Email Security",
                value="logs_inventory:view",
                required=IntegrationHealthCheckRequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        self.assert_components(
            components,
            [
                permissions_checks_expected,
            ],
        )

    @responses.activate
    def test_components_failed(self):
        setup_basic_responses(fail=True)
        components = HealthCheckComponent.get_components(connector=self.connector)

        permissions_checks_expected = [
            HealthCheckRequirement(
                name="Read logs",
                description="Read logs from Mimecast Advanced Email Security",
                value="logs_inventory:view",
                required=IntegrationHealthCheckRequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]

        self.assert_components(
            components,
            [
                permissions_checks_expected,
            ],
        )


class MimecastAdvancedEmailSecurityV1HelpersTest(BaseTestCase):
    def test_get_action(self):
        self.assertEqual(event_sync.get_action("blocked"), ControlAction.DENIED)
        self.assertEqual(event_sync.get_action("BLOCKED"), ControlAction.DENIED)
        self.assertEqual(event_sync.get_action("allowed"), ControlAction.UNKNOWN)
        self.assertEqual(event_sync.get_action(""), ControlAction.UNKNOWN)

    def test_get_disposition(self):
        for val in ["block", "drop", "deny"]:
            self.assertEqual(event_sync.get_disposition(val), Disposition.DROPPED)
            self.assertEqual(
                event_sync.get_disposition(val.upper()), Disposition.DROPPED
            )

        for val in ["quarantine", "hold", "suspend"]:
            self.assertEqual(event_sync.get_disposition(val), Disposition.QUARANTINED)
            self.assertEqual(
                event_sync.get_disposition(val.upper()), Disposition.QUARANTINED
            )

        for val in ["allow", "approve"]:
            self.assertEqual(event_sync.get_disposition(val), Disposition.ALLOWED)
            self.assertEqual(
                event_sync.get_disposition(val.upper()), Disposition.ALLOWED
            )

        for val in ["alert", "observe"]:
            self.assertEqual(event_sync.get_disposition(val), Disposition.ALERT)
            self.assertEqual(event_sync.get_disposition(val.upper()), Disposition.ALERT)

        self.assertEqual(event_sync.get_disposition("reset"), Disposition.RESET)
        self.assertEqual(event_sync.get_disposition("RESET"), Disposition.RESET)

        self.assertEqual(
            event_sync.get_disposition("unknown_value"), Disposition.UNKNOWN
        )
        self.assertEqual(event_sync.get_disposition(""), Disposition.UNKNOWN)

    def test_get_categories(self):
        self.assertEqual(
            event_sync.get_categories([UrlCategory.PHISHING, "random2"]),
            [UrlCategory.PHISHING, UrlCategory.OTHER],
        )

    def test_get_direction(self):
        self.assertEqual(
            event_sync.get_direction("inbound"), EmailActivityDirection.INBOUND
        )
        self.assertEqual(
            event_sync.get_direction("outbound"), EmailActivityDirection.OUTBOUND
        )
        self.assertEqual(
            event_sync.get_direction("INBOUND"), EmailActivityDirection.UNKNOWN
        )
        self.assertEqual(
            event_sync.get_direction("something else"), EmailActivityDirection.UNKNOWN
        )
        self.assertEqual(event_sync.get_direction(""), EmailActivityDirection.UNKNOWN)

    def test_get_impersonation_type(self):
        # DOMAIN types
        domain_types = [
            "similar_internal_domain",
            "newly_observed_domain",
            "custom_external_domain",
            "mimecast_external_domain",
            "advanced_similar_internal_domain",
            "advanced_custom_external_domain",
            "advanced_mimecast_external_domain",
        ]
        for dt in domain_types:
            self.assertEqual(
                event_sync.get_impersonation_type(dt), OSINTIndicatorType.DOMAIN
            )

        # EMAIL_ADDRESS types
        email_types = [
            "internal_user_name",
            "reply_address_mismatch",
            "custom_name_list",
        ]
        for et in email_types:
            self.assertEqual(
                event_sync.get_impersonation_type(et), OSINTIndicatorType.EMAIL_ADDRESS
            )

        # OTHER types
        self.assertEqual(
            event_sync.get_impersonation_type("some_random_type"),
            OSINTIndicatorType.OTHER,
        )
        self.assertEqual(
            event_sync.get_impersonation_type(""), OSINTIndicatorType.OTHER
        )
        self.assertEqual(
            event_sync.get_impersonation_type("SIMILAR_INTERNAL_DOMAIN"),
            OSINTIndicatorType.OTHER,
        )  # case sensitive

    def test_default_params(self):
        expected = {"meta": {"pagination": {"pageSize": 100}}, "data": [{}]}
        self.assertEqual(mimecast_api.get_params(), expected)

    def test_custom_page_size_and_token(self):
        expected = {
            "meta": {"pagination": {"pageSize": 50, "pageToken": "abc123"}},
            "data": [{}],
        }
        self.assertEqual(
            mimecast_api.get_params(page_size=50, page_token="abc123"), expected
        )

    def test_date_range_only(self):
        expected = {
            "meta": {"pagination": {"pageSize": 100}},
            "data": [{"from": "2023-01-01", "to": "2023-01-31"}],
        }
        self.assertEqual(
            mimecast_api.get_params(from_date="2023-01-01", to_date="2023-01-31"),
            expected,
        )

    def test_all_parameters(self):
        expected = {
            "meta": {"pagination": {"pageSize": 25, "pageToken": "xyz789"}},
            "data": [{"from": "2022-12-01", "to": "2022-12-31"}],
        }
        self.assertEqual(
            mimecast_api.get_params(
                page_size=25,
                page_token="xyz789",
                from_date="2022-12-01",
                to_date="2022-12-31",
            ),
            expected,
        )

    def test_empty_page_size_and_token(self):
        expected = {"meta": {"pagination": {}}, "data": [{}]}
        self.assertEqual(
            mimecast_api.get_params(page_size=None, page_token=None), expected
        )
